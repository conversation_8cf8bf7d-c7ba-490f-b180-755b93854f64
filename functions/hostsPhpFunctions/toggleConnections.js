/*
 * Toggle Connections Visibility on hosts.php page
 * Adds a new button to the header that shows / hides parent-child connection arrows.
 * The visibility state is stored on window.showConnections (default: false).
 */

(function () {
    // Ensure we only run once DOM is ready
    document.addEventListener('DOMContentLoaded', function () {
        // Find the status count wrappers (desktop and mobile)
        const statusCountWrapper = document.querySelector('#status-count-wrapper');
        const apmProgressMobile = document.querySelector('#apm-progress-mobile');
        
        if (!statusCountWrapper) return; // Safety check

        // Create the toggle button with status filter styling
        const toggleBtn = document.createElement('span');
        toggleBtn.className = 'host-count connections-toggle';
        toggleBtn.id = 'toggle-connections-btn';
        toggleBtn.title = 'Toggle Connections';
        toggleBtn.innerHTML = '<i class="fa fa-link"></i>';

        // Create mobile version of the toggle button
        const mobileToggleBtn = document.createElement('span');
        mobileToggleBtn.className = 'host-count connections-toggle';
        mobileToggleBtn.id = 'toggle-connections-btn-mobile';
        mobileToggleBtn.title = 'Toggle Connections';
        mobileToggleBtn.innerHTML = '<i class="fa fa-link"></i>';

        // Insert the button at the end of the status count wrapper (right side)
        statusCountWrapper.appendChild(toggleBtn);
        
        // Insert mobile version after the APM progress mobile element
        if (apmProgressMobile) {
            apmProgressMobile.parentNode.insertBefore(mobileToggleBtn, apmProgressMobile.nextSibling);
        }

        // State variable (default: arrows are visible)
        if (typeof window.showConnections === 'undefined') {
            window.showConnections = true;
        }

        // Helper to update arrow-group visibility & button appearance
        function updateConnectionsVisibility() {
            const arrowGroup = document.querySelector('g.arrow-group');
            if (arrowGroup) {
                arrowGroup.style.display = window.showConnections ? '' : 'none';
            }
            
            // Update both desktop and mobile button appearances
            const buttons = [toggleBtn, mobileToggleBtn];
            buttons.forEach(btn => {
                if (btn) {
                    if (window.showConnections) {
                        btn.classList.remove('connection-off');
                        btn.classList.add('active');
                    } else {
                        btn.classList.add('connection-off');
                        btn.classList.remove('active');
                    }
                }
            });
        }

        // Toggle logic for desktop
        toggleBtn.addEventListener('click', function (e) {
            e.preventDefault();
            window.showConnections = !window.showConnections;
            updateConnectionsVisibility();
        });

        // Toggle logic for mobile
        if (mobileToggleBtn) {
            mobileToggleBtn.addEventListener('click', function (e) {
                e.preventDefault();
                window.showConnections = !window.showConnections;
                updateConnectionsVisibility();
            });
        }

        // Initial attempt (arrowGroup may not exist yet)
        updateConnectionsVisibility();

        // Observe the canvas container for arrowGroup creation / re-render
        const canvasContainer = document.getElementById('canvas-container');
        if (canvasContainer) {
            const observer = new MutationObserver(updateConnectionsVisibility);
            observer.observe(canvasContainer, { childList: true, subtree: true });
        }
    });
})(); 