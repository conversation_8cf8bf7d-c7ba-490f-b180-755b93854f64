<?php
include 'language_selector.php';
include 'theme_loader.php'; // Load chosenTheme variable
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Blesk - Reports</title>
    <!-- Base + page-specific styles -->
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/style.css">
    <link rel="stylesheet" href="styles/<?php echo $chosenTheme; ?>/reports.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">

    <!-- External libs / helpers reused across the project -->
    <script src="functions/d3.v7.min.js"></script>
    <script src="functions/fetchHostGroups.js"></script>
    <script src="functions/helperFunctions.js"></script>
    <script src="functions/translator.js"></script>
    <script src="functions/translationObserver.js"></script>
    <!-- Hostlist modal helper -->
    <script src="functions/hostlistPhpFunctions/modalHandlers.js"></script>
    <!-- Page-specific logic -->
    <script defer src="functions/reportsFunctions/reports.js"></script>
</head>
<body>
    <!-- Page Header -->
    <header>
        <div style="display: flex; align-items: center; gap: 15px;">
            <a href="../../index.php">
                <img style="width: 25px;" src="../../images/blesk-favicon.svg" alt="Home">
            </a>
            <div class="breadcrumbs">
                <a href="infra.php"><i class="fa fa-home"></i> Home</a>
                <span class="separator">/</span>
                <span class="current"><i class="fa fa-file-text"></i> Reports</span>
            </div>
        </div>
        <div class="header-content">
            <div class="hamburger">
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
                <div class="hamburger-line"></div>
            </div>
            <div class="header-buttons">
                <a class="header-button" href="https://<?php echo $_SERVER['HTTP_HOST']; ?>/bubblemaps/infra.php"><i class="fa fa-th"></i> Bubble View</a>
                <a class="header-button" href="hostlist.php"><i class="fa fa-list"></i> List View</a>
                <a class="header-button" id="openSettingsBtn"><i class="fa fa-cog"></i> Advanced Settings</a>
            </div>
        </div>
    </header>
    <!-- Main container -->
    <div class="reports-container">
        <!-- Controls bar -->
        <div class="reports-controls">
            <div class="controls-row primary-controls">
                <!-- Status filter buttons -->
                <div id="reports-status-filters" class="reports-status-filters"></div>
                <div class="vertical-separator"></div>
                <div class="control-group">
                    <label for="report-start">Start</label>
                    <input type="datetime-local" id="report-start">
                </div>
                <div class="control-group">
                    <label for="report-end">End</label>
                    <input type="datetime-local" id="report-end">
                </div>
                <div class="control-group">
                    <label for="report-type">Type</label>
                    <select id="report-type">
                        <option value="hostgroups">Hosts</option>
                        <option value="services">Services</option>
                    </select>
                </div>
                <div class="control-group" id="hostgroup-wrapper">
                    <label for="report-hostgroup">Hostgroup</label>
                    <select id="report-hostgroup">
                        <option value="all">All</option>
                    </select>
                </div>
                <button id="report-generate" class="generate-btn" title="Generate report"><i class="fa fa-magic"></i></button>
                <button id="report-save" class="generate-btn" title="Save PDF report to server"><i class="fa fa-save"></i></button>
                <button id="report-schedule" class="generate-btn" title="Schedule daily email of report"><i class="fa fa-envelope-o"></i></button>
                <button id="view-saved-reports" class="generate-btn" title="View saved reports">
                    <i class="fa fa-folder-open"></i>
                    <span id="saved-reports-badge" class="saved-reports-badge" style="display: none;">0</span>
                </button>
            </div>
        </div>

        <!-- Results / table goes here -->
        <div id="reports-content" class="reports-content">
            <div class="reports-placeholder">Select parameters and click Generate</div>
        </div>
    </div>
    <!-- iframe Modal for credentials and other content -->
    <div id="infoModal" class="iframeModal">
        <div class="iframeModal-content" id="iframeModal-content">
            <span class="iframeMclose">×</span>
            <div class="iframe-loader"></div>
            <iframe id="modal-frame"></iframe>
        </div>
    </div>

    <?php include "settingsModal.php"; ?>

    <div id="scheduleReportModal" class="sr-modal">
        <div class="sr-modal-content">
            <span class="sr-close">×</span>
            <h2>Schedule Report Email</h2>
            <form id="schedule-form">
                <div class="sr-field">
                    <label for="sr-email">Email address(es)</label>
                    <input type="text" id="sr-email" required placeholder="<EMAIL>; <EMAIL>">
                    <small style="color: #666; font-size: 0.8em; margin-top: 2px; display: block;">
                        Separate multiple emails with semicolons (;)
                    </small>
                </div>
                <div class="sr-field">
                    <label for="sr-frequency">Frequency</label>
                    <select id="sr-frequency">
                        <option value="daily">Daily</option>
                        <option value="weekly">Weekly</option>
                        <option value="monthly">Monthly</option>
                    </select>
                </div>
                <div class="sr-field">
                    <label for="sr-time">Time (HH:MM)</label>
                    <input type="time" id="sr-time" value="00:05">
                </div>
                <div class="sr-field">
                    <label for="sr-range">Days in range</label>
                    <input type="number" id="sr-range" min="1" max="365" value="1">
                </div>
                <div class="sr-field">
                    <label>Host Statuses</label>
                    <div class="sr-checkbox-group" id="sr-host-statuses">
                        <label><input type="checkbox" value="up" checked> Up</label>
                        <label><input type="checkbox" value="down" checked> Down</label>
                        <label><input type="checkbox" value="unreachable" checked> Unreach</label>
                    </div>
                </div>
                <div class="sr-field">
                    <label>Service Statuses</label>
                    <div class="sr-checkbox-group" id="sr-svc-statuses">
                        <label><input type="checkbox" value="ok" checked> OK</label>
                        <label><input type="checkbox" value="warning" checked> Warn</label>
                        <label><input type="checkbox" value="critical" checked> Crit</label>
                        <label><input type="checkbox" value="unknown" checked> Unk</label>
                    </div>
                </div>
                <div class="sr-field">
                    <label>
                        <input type="checkbox" id="sr-save-to-server" checked> Save report to server
                    </label>
                    <small style="color: #666; font-size: 0.8em; margin-top: 2px; display: block;">
                        Reports will be accessible from the Reports page
                    </small>
                </div>
                <div id="sr-status" class="sr-status"></div>
                <div class="sr-actions">
                    <button type="button" id="sr-send-now" class="generate-btn">Send Now</button>
                    <button type="button" id="sr-cancel" class="generate-btn">Cancel</button>
                    <button type="button" id="sr-disable" class="generate-btn red" style="display:none;">Disable</button>
                    <button type="submit" id="sr-save" class="generate-btn">Save</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Saved Reports Modal -->
    <div id="savedReportsModal" class="sr-modal">
        <div class="sr-modal-content" style="min-width: 600px; max-width: 800px;">
            <span class="sr-close">×</span>
            <h2>Saved Reports</h2>
            <div id="saved-reports-container" class="saved-reports-container">
                <div class="saved-reports-loading">Loading saved reports...</div>
            </div>
        </div>
    </div>

    <!-- Save Report Name Modal -->
    <div id="saveReportNameModal" class="sr-modal">
        <div class="sr-modal-content">
            <span class="sr-close">×</span>
            <h2>Save Report</h2>
            <form id="save-report-form">
                <div class="sr-field">
                    <label for="report-name">Report Name (Optional)</label>
                    <input type="text" id="report-name" placeholder="Enter a custom name for this report">
                    <small style="color: #666; font-size: 0.8em; margin-top: 2px; display: block;">
                        Leave blank to use default naming
                    </small>
                </div>
                <div class="sr-actions">
                    <button type="button" id="save-report-cancel" class="generate-btn">Cancel</button>
                    <button type="submit" id="save-report-confirm" class="generate-btn">Save Report</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Hamburger toggle script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const hamburger = document.querySelector('.hamburger');
            const headerButtons = document.querySelector('.header-buttons');
            if (hamburger) {
                hamburger.addEventListener('click', function(e) {
                    e.stopPropagation();
                    this.classList.toggle('active');
                    headerButtons.classList.toggle('active');
                });
                document.addEventListener('click', function(event) {
                    if (!event.target.closest('.header-content')) {
                        hamburger.classList.remove('active');
                        headerButtons.classList.remove('active');
                    }
                });
            }
        });
        // Initialize the translator
        const dictionaries = <?php echo json_encode($dictionaries); ?>;
        const selectedLang = '<?php echo $selectedLang; ?>';
        const translator = new Translator(dictionaries, selectedLang);
        translator.init();
        initTranslationObserver(translator);
    </script>
</body>
</html>
